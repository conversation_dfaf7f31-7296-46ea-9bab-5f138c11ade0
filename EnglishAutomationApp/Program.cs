using System;
using System.Threading.Tasks;
using System.Windows.Forms;
using EnglishAutomationApp.Data;
using EnglishAutomationApp.Helpers;
using EnglishAutomationApp.Views;

namespace EnglishAutomationApp
{
    internal static class Program
    {
        [STAThread]
        static void Main()
        {
            // Enable visual styles
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            // Initialize error handling
            ErrorHandler.Initialize();

            // Initialize database
            InitializeDatabase().Wait();

            // Start the application with login form
            Application.Run(new LoginForm());
        }

        private static async Task InitializeDatabase()
        {
            try
            {
                // Initialize Access database
                await Data.AccessDatabaseHelper.InitializeDatabaseAsync();

                ErrorHandler.LogInfo("Access database initialized successfully.");
            }
            catch (FileNotFoundException ex)
            {
                MessageBox.Show($"Database file not found!\n\n{ex.Message}\n\nPlease:\n1. Create an Access database file named 'eng-otomasyon.accdb'\n2. Place it in your Documents folder or in the application directory\n3. Ensure it contains the required tables",
                    "Database Not Found", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                ErrorHandler.LogError(ex, "Database file not found");
                Application.Exit();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Database initialization error: {ex.Message}\n\nPlease ensure:\n1. Microsoft Access Database Engine is installed\n2. Database file exists and is accessible\n3. Database contains required tables",
                    "Database Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                ErrorHandler.LogError(ex, "Database initialization failed");
                Application.Exit();
            }
        }


    }
}
